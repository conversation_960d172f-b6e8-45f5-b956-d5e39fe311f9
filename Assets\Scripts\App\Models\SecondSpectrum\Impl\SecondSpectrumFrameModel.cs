using System;
using App.Models.Configuration;
using App.Models.SecondSpectrum.Impl.Data;
using App.Models.SecondSpectrum.Impl.Handler;
using App.Models.SecondSpectrum.Impl.Services;
using App.Models.SecondSpectrum.Impl.Services.Impl;
using App.Models.SecondSpectrum.Impl.Services.Synchronizer;
using App.Models.SecondSpectrum.Impl.Services.Synchronizer.Impl;
using App.Types;
using CoreTech.Promises;
using CoreTech.Promises.Impl;
using Framework.Models.Base.Impl;
using UnityEngine;
using UnityEngine.Networking;
using FrameDebugger;
using CoreTech.CoroutineUtilities;
using System.Collections.Generic;
using System.Linq;

namespace App.Models.SecondSpectrum.Impl
{
    public class SecondSpectrumFrameModel : Model, ISecondSpectrumFrameModel
    {
        public bool LogBinaryId = false;
        private ISecondSpectrumBinaryDeserializer binaryDeserializer;
        private ICacheBinaryDataConfigurator cacheBinary;
        private IDataSynchronizerInternal dataSynchronizer;

        private string baseUrl;
        private long currentFrameIdx;
        private bool useJsonFormat = true; // Switch to use new JSON format
        
        protected override void Initialize()
        {
            cacheBinary = new CacheBinaryData();
            dataSynchronizer = new DataSynchronizerInternal(cacheBinary);
            binaryDeserializer = new SecondSpectrumBinaryDeserializer(CoreTech.BinaryHttpService);
            
            binaryDeserializer.DataReceived += OnDataReceived;
            dataSynchronizer.Updated += OnDataUpdated;
            CoreTech.RouterService.Subscribe<ClipState>(ClipStateChanged);
        }
        
        protected override void Dispose()
        {
            binaryDeserializer.DataReceived -= OnDataReceived;
            dataSynchronizer.Updated -= OnDataUpdated;
            CoreTech.RouterService.Unsubscribe<ClipState>(ClipStateChanged);
        }

        protected override void DoUpdate()
        {
            try
            {
                dataSynchronizer.DoUpdate();
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }

            if (useJsonFormat)
            {
                // For JSON format, we increment frame index sequentially
                if (cacheBinary.TryGetNextBinaryID(out var nextFrameIdx))
                {
                    currentFrameIdx = nextFrameIdx;
                    FetchJsonFrame(currentFrameIdx);
                }
            }
            else
            {
                if (cacheBinary.TryGetNextBinaryID(out var binaryId))
                {
                    FetchBinary(binaryId);
                }
            }
        }

        public IPromise StartFetching(long initialBinaryId, string baseUrl)
        {
            cacheBinary.ClearCache();
            dataSynchronizer.Reset();

            this.baseUrl = baseUrl;
            this.currentFrameIdx = initialBinaryId; // Now treating as frame index
            var cacheSizeLimit = 0;

            if (Get<IConfigurationModel>().Configuration == null)
            {
                cacheSizeLimit = 200;
            }
            else
            {
                cacheSizeLimit = Get<IConfigurationModel>().Configuration.CacheSizeLimit;
            }

            cacheBinary.SetCacheSizeLimit(cacheSizeLimit);

            IPromise<long> outcome;
            if (useJsonFormat)
            {
                outcome = FetchJsonFrame(currentFrameIdx);
            }
            else
            {
                outcome = FetchBinary(initialBinaryId);
            }

            return new PromiseWrapper<long>(outcome);
        }

        public void SetNextBinaryId(long nextBinaryId)
        {
            cacheBinary.ClearCache();
            dataSynchronizer.Reset();

            if (useJsonFormat)
            {
                currentFrameIdx = nextBinaryId; // Treat as frame index
            }

            cacheBinary.NextBinaryIdReceived(nextBinaryId);
        }
        
        private IPromise<long> FetchBinary(long binaryId)
        {
            var url = $"{baseUrl}{binaryId}";
            if(LogBinaryId) Debug.Log($"Fetching Binary Id: {binaryId}\nUrl:{url}");
            return binaryDeserializer.RequestBinary(url).Then(cacheBinary.NextBinaryIdReceived);
        }

        private IPromise<long> FetchJsonFrame(long frameIdx)
        {
            var url = $"{baseUrl}{frameIdx}.json";
            if(LogBinaryId) Debug.Log($"Fetching JSON Frame: {frameIdx}\nUrl:{url}");

            var promise = new Promise<long>();

            CoroutineProvider.StartCoroutine(FetchJsonCoroutine(url, frameIdx, promise));

            return promise;
        }

        private System.Collections.IEnumerator FetchJsonCoroutine(string url, long frameIdx, Promise<long> promise)
        {
            using (UnityWebRequest request = UnityWebRequest.Get(url))
            {
                request.timeout = 30;
                yield return request.SendWebRequest();

                if (request.result == UnityWebRequest.Result.Success)
                {
                    try
                    {
                        string jsonText = request.downloadHandler.text;
                        var frameData = JsonUtility.FromJson<FrameData>(jsonText);

                        // Convert JSON frame data to the existing SecondSpectrum format
                        ConvertJsonToSecondSpectrumData(frameData);

                        // Return next frame index (sequential)
                        long nextFrameIdx = frameIdx + 1;
                        cacheBinary.NextBinaryIdReceived(nextFrameIdx);
                        promise.Dispatch(nextFrameIdx);
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"Failed to parse JSON frame {frameIdx}: {ex.Message}");
                        promise.ReportFail(ex);
                    }
                }
                else
                {
                    Debug.LogError($"Failed to fetch JSON frame {frameIdx}: {request.error}");
                    promise.ReportFail(new Exception($"HTTP Error: {request.error}"));
                }
            }
        }

        private void ConvertJsonToSecondSpectrumData(FrameData frameData)
        {
            // Convert JSON frame data to the existing SecondSpectrum tuple format
            // The existing system expects: Tuple<int[], long[], float[], byte[]>

            var ints = new List<int>();
            var longs = new List<long>();
            var floats = new List<float>();
            var bytes = new List<byte>();

            // Add frame metadata
            ints.Add(frameData.frameIdx);
            ints.Add(frameData.period);
            longs.Add(frameData.wallClock);

            if (frameData.gameClock != 0)
            {
                longs.Add((long)(frameData.gameClock * 1000)); // Convert to milliseconds
            }
            else
            {
                longs.Add(0);
            }

            // Add ball data if available
            if (frameData.ball != null && frameData.ball.Length >= 3)
            {
                floats.AddRange(frameData.ball);
            }

            // Add player data
            if (frameData.players != null)
            {
                foreach (var player in frameData.players)
                {
                    // Add player position
                    if (player.xyz != null && player.xyz.Length >= 3)
                    {
                        floats.AddRange(player.xyz);
                    }

                    // Add player metadata as bytes (simplified)
                    if (!string.IsNullOrEmpty(player.playerId))
                    {
                        bytes.AddRange(System.Text.Encoding.UTF8.GetBytes(player.playerId));
                        bytes.Add(0); // Null terminator
                    }

                    if (!string.IsNullOrEmpty(player.jerseyNumber))
                    {
                        if (int.TryParse(player.jerseyNumber, out int jerseyNum))
                        {
                            ints.Add(jerseyNum);
                        }
                    }

                    // Add keypoint data
                    if (player.keypoints != null)
                    {
                        foreach (var keypoint in player.keypoints)
                        {
                            if (keypoint.xyz != null && keypoint.xyz.Length >= 3)
                            {
                                floats.AddRange(keypoint.xyz);
                            }
                        }
                    }
                }
            }

            // Create the tuple and trigger the data received event
            var data = Tuple.Create(ints.ToArray(), longs.ToArray(), floats.ToArray(), bytes.ToArray());
            OnDataReceived(data);
        }

        private void OnDataReceived(Tuple<int[], long[], float[], byte[]> data)
        {
            var secondSpectrumData = new SecondSpectrumData(data.Item1, data.Item2, data.Item3, data.Item4);

            cacheBinary.DataReceived(secondSpectrumData);
        }
        
        private void OnDataUpdated(ISecondSpectrumData data)
        {
            if(data == null) return;
            CoreTech.RouterService.Publish(data);
        }
        
        private void ClipStateChanged(ClipState clipState)
        {
            if (clipState == ClipState.Running)
            {
                dataSynchronizer.Reset();
            }
        }
    }
}