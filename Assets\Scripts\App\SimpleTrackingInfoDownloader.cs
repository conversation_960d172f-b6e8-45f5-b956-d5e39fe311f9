using System;
using System.Collections;
using System.IO;
using UnityEngine;
using UnityEngine.Networking;

public class SimpleTrackingInfoDownloader : MonoBehaviour
{
    [Header("Download Settings")]
    public string baseUrl = "https://dy3jcbcujp07o.cloudfront.net";
    public string gameId = "2292817"; // Default from SandboxData
    public bool downloadOnStart = true;

    [Header("Alternative URLs to try")]
    public string[] alternativeBaseUrls = {
        "https://d2grtshlowl8y9.cloudfront.net", // From SandboxData debug URL
        "https://dy3jcbcujp07o.cloudfront.net"
    };
    
    private string trackingInfoPath;
    
    void Start()
    {
        // Create tracking-info folder in Assets
        trackingInfoPath = Path.Combine(Application.dataPath, "tracking-info");
        
        if (downloadOnStart)
        {
            StartCoroutine(DownloadTrackingInfo());
        }
    }
    
    [ContextMenu("Download Tracking Info")]
    public void StartDownload()
    {
        StartCoroutine(DownloadTrackingInfo());
    }
    
    IEnumerator DownloadTrackingInfo()
    {
        Debug.Log($"Starting download of tracking-info for game {gameId}");

        // Test internet connectivity first
        yield return StartCoroutine(TestConnectivity());

        // Create directory if it doesn't exist
        if (!Directory.Exists(trackingInfoPath))
        {
            Directory.CreateDirectory(trackingInfoPath);
            Debug.Log($"Created directory: {trackingInfoPath}");
        }

        // List of files to try downloading (most likely to exist first)
        string[] filesToTry = {
            "metadata.json",  // Most likely to exist
            "tracking-data.bin",
            "player-positions.json",
            "ball-tracking.json",
            "events.json",
            "timeline.json",
            "game-info.json",
            "manifest.json",
            "config.json"
        };

        int totalSuccessCount = 0;

        // Try each base URL
        foreach (string currentBaseUrl in alternativeBaseUrls)
        {
            Debug.Log($"Trying base URL: {currentBaseUrl}");

            // Try tracking-info folder first
            string trackingInfoUrl = $"{currentBaseUrl}/{gameId}/tracking-info/";
            int successCount = 0;

            foreach (string fileName in filesToTry)
            {
                string fileUrl = trackingInfoUrl + fileName;
                yield return StartCoroutine(DownloadFile(fileUrl, fileName, (success) => {
                    if (success)
                    {
                        successCount++;
                        totalSuccessCount++;
                    }
                }));
            }

            Debug.Log($"Downloaded {successCount} files from {currentBaseUrl}/tracking-info/");

            // Also try tracking-pose folder
            yield return StartCoroutine(DownloadFromTrackingPose(currentBaseUrl));

            // If we found files, no need to try other base URLs
            if (successCount > 0)
            {
                Debug.Log($"Found files with {currentBaseUrl}, skipping other URLs");
                break;
            }
        }

        Debug.Log($"Download completed! Total files downloaded: {totalSuccessCount} to: {trackingInfoPath}");

        // Refresh Unity's asset database to show the new files
        #if UNITY_EDITOR
        UnityEditor.AssetDatabase.Refresh();
        #endif
    }
    
    IEnumerator DownloadFromTrackingPose(string currentBaseUrl)
    {
        Debug.Log($"Also trying tracking-pose folder structure with {currentBaseUrl}...");

        string trackingPoseUrl = $"{currentBaseUrl}/{gameId}/tracking-pose/metadata.json";

        yield return StartCoroutine(DownloadFile(trackingPoseUrl, $"metadata-from-tracking-pose-{currentBaseUrl.GetHashCode()}.json", (success) => {
            if (success) Debug.Log($"Also downloaded metadata from tracking-pose folder using {currentBaseUrl}");
        }));
    }
    
    IEnumerator DownloadFile(string url, string fileName, System.Action<bool> onComplete)
    {
        Debug.Log($"Attempting to download: {url}");

        using (UnityWebRequest request = UnityWebRequest.Get(url))
        {
            // Set a timeout
            request.timeout = 30;

            yield return request.SendWebRequest();

            if (request.result == UnityWebRequest.Result.Success)
            {
                try
                {
                    string filePath = Path.Combine(trackingInfoPath, fileName);
                    File.WriteAllBytes(filePath, request.downloadHandler.data);
                    Debug.Log($"✓ Downloaded: {fileName} ({request.downloadHandler.data.Length} bytes)");
                    onComplete?.Invoke(true);
                }
                catch (Exception ex)
                {
                    Debug.LogError($"✗ Failed to save {fileName}: {ex.Message}");
                    onComplete?.Invoke(false);
                }
            }
            else
            {
                // Only show detailed errors for important files or unexpected errors
                if (fileName.Contains("metadata") || request.responseCode != 404)
                {
                    string errorDetails = $"Error: {request.error}, Response Code: {request.responseCode}";
                    if (request.result == UnityWebRequest.Result.ConnectionError)
                    {
                        errorDetails += " (Connection Error)";
                    }
                    else if (request.result == UnityWebRequest.Result.ProtocolError && request.responseCode == 404)
                    {
                        errorDetails += " (File not found - this is normal for optional files)";
                    }
                    else if (request.result == UnityWebRequest.Result.ProtocolError)
                    {
                        errorDetails += " (Protocol Error)";
                    }

                    Debug.LogWarning($"✗ Failed to download {fileName}: {errorDetails}");
                }
                else
                {
                    // Just a quiet log for expected missing files
                    Debug.Log($"- {fileName} not available (404)");
                }

                onComplete?.Invoke(false);
            }
        }
    }
    
    IEnumerator TestConnectivity()
    {
        Debug.Log("Testing internet connectivity...");

        using (UnityWebRequest request = UnityWebRequest.Get("https://www.google.com"))
        {
            request.timeout = 10;
            yield return request.SendWebRequest();

            if (request.result == UnityWebRequest.Result.Success)
            {
                Debug.Log("✓ Internet connectivity confirmed");
            }
            else
            {
                Debug.LogWarning($"⚠ Internet connectivity test failed: {request.error}");
                Debug.LogWarning("Proceeding anyway, but downloads may fail...");
            }
        }
    }

    void Update()
    {
        // Press 'D' key to start download manually
        if (Input.GetKeyDown(KeyCode.D))
        {
            StartDownload();
        }
    }
}
