#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;

public class CreateTrackingInfoDownloader
{
    [MenuItem("Tools/Create Tracking Info Downloader")]
    static void CreateDownloader()
    {
        // Create a new GameObject
        GameObject downloaderObj = new GameObject("TrackingInfoDownloader");
        
        // Add the downloader component
        SimpleTrackingInfoDownloader downloader = downloaderObj.AddComponent<SimpleTrackingInfoDownloader>();
        
        // Set default values
        downloader.baseUrl = "https://dy3jcbcujp07o.cloudfront.net";
        downloader.gameId = "2292817";
        downloader.downloadOnStart = true;
        
        // Select the object in the hierarchy
        Selection.activeGameObject = downloaderObj;
        
        Debug.Log("Created TrackingInfoDownloader GameObject. It will download on Start, or press 'D' key, or use the context menu.");
    }
}
#endif
