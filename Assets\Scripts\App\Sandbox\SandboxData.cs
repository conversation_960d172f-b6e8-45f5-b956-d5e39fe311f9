using App.Components.UI.Timeline;
using App.Models.Clips.Impl.Tasks.Info.MatchMetadata.Impl.Data;
using App.Models.MatchMetaData.Impl;
using App.Models.SecondSpectrum.Impl;
using CoreTech.Promises;
using CoreTech.Promises.Impl;
using UnityEngine;

namespace App.Sandbox
{
    public class SandboxData : MonoBehaviour
    {
        [SerializeField]
        private string debugBinarySecSpecUrl = "https://d26ltfcw6igk13.cloudfront.net/2292817/";

        [SerializeField] private long debugBinaryId = 1659794539000;

        [SerializeField]
        private string debugMetaDataUrl = "https://d26ltfcw6igk13.cloudfront.net/2292817/metadata.json";

        [SerializeField] private bool logBinaryId = true;
        [SerializeField] private bool useDebugUrls = false;

        private SecondSpectrumFrameModel secondSpectrumFrameModel;
        private string baseUrl = "https://d26ltfcw6igk13.cloudfront.net";

        private bool lastLogBinaryId;
        private string gameId;
        private long binaryId;

        private void Awake()
        {
            binaryId = debugBinaryId;
            lastLogBinaryId = logBinaryId;
        }

        private void Update()
        {
            if (logBinaryId != lastLogBinaryId)
            {
                lastLogBinaryId = logBinaryId;
                UpdateModelSettings();
            }
        }

        public void SetGameId(string value) => gameId = value;
        public void SetUseDebugUrls(bool value) => useDebugUrls = value;

        public IPromise StartFetching(SecondSpectrumFrameModel secondSpectrumFrameModel, long binaryId = 0)
        {
            this.secondSpectrumFrameModel = secondSpectrumFrameModel;
            if (!useDebugUrls)
            {
                this.binaryId = binaryId;
            }
            UpdateModelSettings();
            var url = useDebugUrls ? debugBinarySecSpecUrl : $"{baseUrl}/{gameId}/tracking-pose/";
            return secondSpectrumFrameModel
                .StartFetching(this.binaryId, url)
                .Then(() => Debug.Log("Fetching Initial Binary Data Completed"));
        }

        public IPromise LoadGameMetaData(MatchMetaDataModel matchModel)
        {
            var metadataUrl = useDebugUrls ? debugMetaDataUrl : $"{baseUrl}/{gameId}/tracking-pose/metadata.json";
            return new PromiseWrapper<IMatchMetadata>(matchModel.Fetch(metadataUrl, ""));
        }

        private void UpdateModelSettings()
        {
            if (secondSpectrumFrameModel == null) return;
            Debug.Log($"Changing Log Binary id to: {logBinaryId}");
            secondSpectrumFrameModel.LogBinaryId = logBinaryId;
        }

        public void InitTimeline(TimelineDataService timelineDataService)
        {
            timelineDataService.Initialize(binaryId);
        }

        public void NextBinary(long nextBinaryId)
        {
            secondSpectrumFrameModel.SetNextBinaryId(nextBinaryId);
        }
    }
}